import { useState, useEffect } from "react";
import { X, Download } from "lucide-react";
import { exportLeadsToExcel, getExportFormats } from "../../utils/excelUtils";
import { toast } from 'react-toastify';

const CustomerServiceExportModal = ({
  isOpen,
  onClose,
  title = "Export Data",
  buttonText = "Export Data",
}) => {
  const [selectedFormat, setSelectedFormat] = useState('xlsx');
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportStatus, setExportStatus] = useState('');

  const exportFormats = getExportFormats();

  // Handle export
  const handleExport = async () => {
    if (isExporting) return;

    setIsExporting(true);
    setExportProgress(0);
    setExportStatus('Initializing export...');

    try {
      const success = await exportLeadsToExcel(
        "", // No search query for customer service
        (progress, status) => {
          setExportProgress(progress);
          setExportStatus(status);
        }
      );

      if (success) {
        toast.success('Data exported successfully!');
        
        // Auto-close modal after successful export
        setTimeout(() => {
          handleClose();
        }, 1500);
      }
    } catch (error) {
      console.error('Export failed:', error);
      toast.error(error.message || 'Export failed. Please try again.');
      setExportStatus('Export failed');
    } finally {
      setIsExporting(false);
      setExportProgress(0);
      setExportStatus('');
    }
  };

  // Handle close
  const handleClose = () => {
    if (isExporting) return; // Prevent closing during export
    
    setSelectedFormat('xlsx');
    setIsExporting(false);
    setExportProgress(0);
    setExportStatus('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-[rgba(0,0,0,0.7)] flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {title}
          </h2>
          <button
            onClick={handleClose}
            disabled={isExporting}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Export Format */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Export Format
            </label>
            <div className="grid grid-cols-1 gap-2">
              {exportFormats.map((format) => (
                <label
                  key={format.value}
                  className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedFormat === format.value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                  } ${isExporting ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <input
                    type="radio"
                    name="format"
                    value={format.value}
                    checked={selectedFormat === format.value}
                    onChange={(e) => setSelectedFormat(e.target.value)}
                    disabled={isExporting}
                    className="sr-only"
                  />
                  <span className="text-lg mr-3">{format.icon}</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {format.label}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Progress Bar */}
          {isExporting && (
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Export Progress
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {exportProgress}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${exportProgress}%` }}
                ></div>
              </div>
              <p className="mt-2 text-xs text-gray-600 dark:text-gray-400">
                {exportStatus}
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleClose}
            disabled={isExporting}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
          <button
            onClick={handleExport}
            disabled={isExporting}
            className="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Exporting...
              </>
            ) : (
              <>
                <Download size={16} className="mr-2" />
                {buttonText}
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CustomerServiceExportModal;
