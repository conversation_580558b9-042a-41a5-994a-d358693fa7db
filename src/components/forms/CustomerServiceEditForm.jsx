import { useState, useEffect } from "react";
import Select from "react-select";

const CustomerServiceEditForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    customer_name: "",
    account_number: "",
    phone_number: "",
    assigned_agent: "",
    rm_code: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fake data for agents (will be replaced with API data later)
  const agents = [
    { value: "alice_mwangi", label: "<PERSON>wang<PERSON>" },
    { value: "bob_kiprotich", label: "<PERSON>" },
    { value: "carol_wanjiku", label: "<PERSON>" },
    { value: "david_kimani", label: "<PERSON>" },
    { value: "grace_njeri", label: "Grace Njeri" },
    { value: "james_o<PERSON>i", label: "<PERSON>" },
    { value: "sarah_mbatha", label: "<PERSON>" },
  ];

  // React Select styles to match the design from LeadForm
  const selectStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: "48px",
      border: state.isFocused
        ? "2px solid #10b981"
        : errors.assigned_agent
        ? "1px solid #ef4444"
        : "1px solid #d1d5db",
      borderRadius: "8px",
      boxShadow: "none",
      "&:hover": {
        borderColor: state.isFocused ? "#10b981" : "#9ca3af",
      },
      backgroundColor: "white",
      fontSize: "14px",
    }),
    placeholder: (provided) => ({
      ...provided,
      color: "#9ca3af",
    }),
    singleValue: (provided) => ({
      ...provided,
      color: "#111827",
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 9999,
      border: "1px solid #e5e7eb",
      borderRadius: "8px",
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "#10b981"
        : state.isFocused
        ? "#f0fdf4"
        : "white",
      color: state.isSelected ? "white" : "#374151",
      "&:hover": {
        backgroundColor: state.isSelected ? "#10b981" : "#f0fdf4",
      },
    }),
  };

  // Populate form with item data for editing
  useEffect(() => {
    if (item) {
      setFormData({
        customer_name: item.customer_name || "",
        account_number: item.account_number || "",
        phone_number: item.phone_number || "",
        assigned_agent: item.assigned_agent || "",
        rm_code: item.rm_code || "",
      });
    }
  }, [item]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.customer_name.trim()) {
      newErrors.customer_name = "Customer name is required";
    }

    if (!formData.account_number.trim()) {
      newErrors.account_number = "Account number is required";
    }

    if (!formData.phone_number.trim()) {
      newErrors.phone_number = "Phone number is required";
    } else if (!/^(\+254|0)[17]\d{8}$/.test(formData.phone_number)) {
      newErrors.phone_number = "Please enter a valid Kenyan phone number";
    }

    if (!formData.assigned_agent.trim()) {
      newErrors.assigned_agent = "Assigned agent is required";
    }

    if (!formData.rm_code.trim()) {
      newErrors.rm_code = "RM code is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handleSelectChange = (selectedOption, actionMeta) => {
    const { name } = actionMeta;
    const value = selectedOption ? selectedOption.label : "";
    
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user selects
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      console.log("Customer updated:", formData);
      
      if (onSubmit) {
        onSubmit(formData);
      }

      onClose();
    } catch (error) {
      console.error("Error updating customer:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Customer Name and Account Number - Side by Side */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Customer Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Customer Name *
          </label>
          <input
            type="text"
            name="customer_name"
            value={formData.customer_name}
            onChange={handleChange}
            className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
              errors.customer_name
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter customer name"
          />
          {errors.customer_name && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.customer_name}
            </p>
          )}
        </div>

        {/* Account Number */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Account Number *
          </label>
          <input
            type="text"
            name="account_number"
            value={formData.account_number}
            onChange={handleChange}
            className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
              errors.account_number
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter account number"
          />
          {errors.account_number && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.account_number}
            </p>
          )}
        </div>
      </div>

      {/* Phone Number and RM Code - Side by Side */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Phone Number */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Phone Number *
          </label>
          <input
            type="tel"
            name="phone_number"
            value={formData.phone_number}
            onChange={handleChange}
            className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
              errors.phone_number
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="e.g., **********"
          />
          {errors.phone_number && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.phone_number}
            </p>
          )}
        </div>

        {/* RM Code */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            RM Code *
          </label>
          <input
            type="text"
            name="rm_code"
            value={formData.rm_code}
            onChange={handleChange}
            className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
              errors.rm_code
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter RM code"
          />
          {errors.rm_code && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.rm_code}
            </p>
          )}
        </div>
      </div>

      {/* Assigned Agent */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Assigned Agent *
        </label>
        <Select
          name="assigned_agent"
          value={agents.find(
            (agent) => agent.label === formData.assigned_agent
          )}
          onChange={handleSelectChange}
          options={agents}
          styles={selectStyles}
          placeholder="Select assigned agent"
          isSearchable
          className="react-select-container"
          classNamePrefix="react-select"
        />
        {errors.assigned_agent && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {errors.assigned_agent}
          </p>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
        <button
          type="button"
          onClick={onClose}
          disabled={isSubmitting}
          className="px-6 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-6 py-3 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Updating...
            </>
          ) : (
            "Update Customer"
          )}
        </button>
      </div>
    </form>
  );
};

export default CustomerServiceEditForm;
