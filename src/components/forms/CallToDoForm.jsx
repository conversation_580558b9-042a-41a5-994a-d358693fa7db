import { useState, useEffect } from "react";
import {
  Phone,
  PhoneMissed,
  Mic,
  MicOff,
  Pause,
  Play,
  Upload,
  X,
} from "lucide-react";
import Select from "react-select";
import {
  purposesService,
  formatPurposesForTable,
} from "../../services/purposesService";
import instance from "../../axios/instance";

const CallToDoForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    callType: "",
    callStatus: "",
    purposeId: "",
    notes: "",
    followUpDate: "",
    followUpTime: "",
    attachments: [],
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Call state management
  const [isCallActive, setIsCallActive] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [callTimer, setCallTimer] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isOnHold, setIsOnHold] = useState(false);

  // Purposes state
  const [purposes, setPurposes] = useState([]);
  const [purposesLoading, setPurposesLoading] = useState(false);

  // Call types and statuses
  const callTypes = [
    { value: "outbound", label: "Outbound" },
    { value: "inbound", label: "Inbound" },
  ];

  const callStatuses = [
    { value: "completed", label: "Completed" },
    { value: "no_answer", label: "No Answer" },
    { value: "busy", label: "Busy" },
    { value: "voicemail", label: "Voicemail" },
    { value: "wrong_number", label: "Wrong Number" },
    { value: "callback_requested", label: "Callback Requested" },
  ];

  // Fetch purposes on component mount
  const fetchPurposes = async () => {
    try {
      setPurposesLoading(true);
      const response = await purposesService.getAll();
      const formattedPurposes = formatPurposesForTable(response);

      // Convert to React Select format
      const purposeOptions = formattedPurposes.map((purpose) => ({
        value: purpose.id,
        label: purpose.name,
        purpose: purpose,
      }));

      setPurposes(purposeOptions);
    } catch (error) {
      console.error("Error fetching purposes:", error);
      setPurposes([]);
    } finally {
      setPurposesLoading(false);
    }
  };

  useEffect(() => {
    fetchPurposes();
  }, []);

  // Timer effect for call duration
  useEffect(() => {
    let interval;
    if (isCallActive && !isOnHold) {
      interval = setInterval(() => {
        setCallTimer((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isCallActive, isOnHold]);

  // Format timer display
  const formatTimer = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Create FormData for file uploads
      const formDataToSend = new FormData();

      // Add basic form fields
      formDataToSend.append("lead_id", item.id);
      formDataToSend.append("call_type", formData.callType);
      formDataToSend.append("call_status", formData.callStatus);
      formDataToSend.append("purpose_id", formData.purposeId);
      formDataToSend.append("notes", formData.notes);
      formDataToSend.append("call_duration", callTimer);

      // Add follow-up date and time if provided
      if (formData.followUpDate && formData.followUpTime) {
        const followUpDateTime = `${formData.followUpDate}T${formData.followUpTime}:00`;
        formDataToSend.append("follow_up_date", followUpDateTime);
      }

      // Add attachments
      formData.attachments.forEach((attachment, index) => {
        formDataToSend.append(`attachments[${index}]`, attachment.file);
      });

      console.log("=== FORM SUBMISSION DATA ===");
      console.log("Customer ID:", item.id);
      console.log("Call Type:", formData.callType);
      console.log("Call Status:", formData.callStatus);
      console.log("Purpose ID:", formData.purposeId);
      console.log("Notes:", formData.notes);
      console.log("Call Duration:", callTimer);
      console.log("Follow-up Date:", formData.followUpDate);
      console.log("Follow-up Time:", formData.followUpTime);
      console.log("Attachments:", formData.attachments);
      console.log("============================");

      // Make API call to /call-activities
      try {
        console.log("Sending data to /call-activities endpoint...");
        const response = await instance.post(
          "/call-activities",
          formDataToSend,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        console.log("API Response:", response.data);
        console.log("Call activity created successfully!");

        // Call the onSubmit callback with the response data
        onSubmit?.(response.data, item);
        onClose();
      } catch (apiError) {
        console.error("API Error:", apiError);
        console.error("Error response:", apiError.response?.data);

        // Still call onSubmit for now, but with error info
        onSubmit?.(formDataToSend, item, apiError);

        // You might want to show an error message to the user here
        // For now, we'll still close the modal
        onClose();
      }
    } catch (error) {
      console.error("Error preparing form data:", error);
      onSubmit?.(null, item, error);
      onClose();
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.callType) {
      newErrors.callType = "Call type is required";
    }

    if (!formData.callStatus) {
      newErrors.callStatus = "Call status is required";
    }

    if (!formData.purposeId) {
      newErrors.purposeId = "Purpose is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // File handling functions
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    const newAttachments = files.map((file) => ({
      id: Date.now() + Math.random(),
      file: file,
      name: file.name,
      size: file.size,
      type: file.type,
    }));

    setFormData((prev) => ({
      ...prev,
      attachments: [...prev.attachments, ...newAttachments],
    }));

    // Clear the input
    e.target.value = "";
  };

  const removeAttachment = (attachmentId) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev.attachments.filter((att) => att.id !== attachmentId),
    }));
  };

  // Call functionality handlers
  const handleStartCall = async () => {
    setIsConnecting(true);

    try {
      // Simulate API call with 2 second delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      setIsCallActive(true);
      setCallTimer(0);
      setIsMuted(false);
      setIsOnHold(false);
    } catch (error) {
      console.error("Failed to initiate call:", error);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleEndCall = async () => {
    try {
      setIsCallActive(false);
      setIsConnecting(false);
      setCallTimer(0);
      setIsMuted(false);
      setIsOnHold(false);
    } catch (error) {
      console.error("Failed to end call:", error);
    }
  };

  const handleToggleMute = () => {
    setIsMuted(!isMuted);
  };

  const handleToggleHold = () => {
    setIsOnHold(!isOnHold);
  };

  // React Select styles
  const selectStyles = {
    control: (provided, state) => ({
      ...provided,
      borderColor: state.isFocused ? "#3b82f6" : "#d1d5db",
      boxShadow: state.isFocused ? "0 0 0 1px #3b82f6" : "none",
      "&:hover": {
        borderColor: state.isFocused ? "#3b82f6" : "#9ca3af",
      },
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "#3b82f6"
        : state.isFocused
        ? "#eff6ff"
        : "white",
      color: state.isSelected ? "white" : "#374151",
      "&:hover": {
        backgroundColor: state.isSelected ? "#3b82f6" : "#eff6ff",
      },
    }),
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-6">
      {/* Call Section - Always show at the top */}
      <div className="border-b border-gray-200 dark:border-gray-600 min-h-[100px] flex items-center">
        {!isCallActive ? (
          // Call Button - Aligned to left
          <div className="flex justify-start w-full">
            <button
              type="button"
              onClick={handleStartCall}
              disabled={isConnecting}
              className="inline-flex items-center px-6 py-3 bg-green-500 hover:bg-green-600 disabled:bg-green-400 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm disabled:cursor-not-allowed"
            >
              <Phone size={20} className="mr-2" />
              {isConnecting ? "Calling..." : "Call"}
            </button>
          </div>
        ) : (
          // Call Controls
          <div className="flex justify-between items-center w-full">
            {/* Call Timer */}
            <div className="text-center">
              <div className="text-2xl font-mono text-gray-900 dark:text-white">
                {formatTimer(callTimer)}
              </div>
              <div className="text-sm text-gray-500">Call Duration</div>
            </div>

            <div className="flex flex-col items-center gap-3">
              {/* Call Control Buttons */}
              <div className="flex items-center space-x-3">
                {/* End Call Button */}
                <button
                  type="button"
                  onClick={handleEndCall}
                  className="inline-flex items-center px-6 py-3 font-medium rounded-lg transition-colors duration-200"
                  style={{ backgroundColor: "#f46b68" }}
                  onMouseEnter={(e) =>
                    (e.target.style.backgroundColor = "#e55a57")
                  }
                  onMouseLeave={(e) =>
                    (e.target.style.backgroundColor = "#f46b68")
                  }
                >
                  <PhoneMissed size={16} className="mr-1 text-white" />
                  <span className="text-white">End</span>
                </button>

                {/* Mute/Unmute Button */}
                <button
                  type="button"
                  onClick={handleToggleMute}
                  className={`inline-flex items-center px-6 py-3 font-medium rounded-lg transition-colors duration-200 ${
                    isMuted ? "bg-transparent" : ""
                  }`}
                  style={!isMuted ? { backgroundColor: "#4ade80" } : {}}
                  onMouseEnter={(e) => {
                    if (!isMuted) {
                      e.target.style.backgroundColor = "#22c55e";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isMuted) {
                      e.target.style.backgroundColor = "#4ade80";
                    }
                  }}
                >
                  {isMuted ? (
                    <>
                      <MicOff size={16} className="mr-1 text-gray-500" />
                      <span className="text-gray-500">Unmute</span>
                    </>
                  ) : (
                    <>
                      <Mic size={16} className="mr-1 text-white" />
                      <span className="text-white">Mute</span>
                    </>
                  )}
                </button>

                {/* Hold/Unhold Button */}
                <button
                  type="button"
                  onClick={handleToggleHold}
                  className={`inline-flex items-center px-6 py-3 font-medium rounded-lg transition-colors duration-200 ${
                    isOnHold ? "bg-transparent" : ""
                  }`}
                  style={!isOnHold ? { backgroundColor: "#ffb800" } : {}}
                  onMouseEnter={(e) => {
                    if (!isOnHold) {
                      e.target.style.backgroundColor = "#e6a600";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isOnHold) {
                      e.target.style.backgroundColor = "#ffb800";
                    }
                  }}
                >
                  {isOnHold ? (
                    <>
                      <Play size={16} className="mr-1 text-gray-500" />
                      <span className="text-gray-500">Unhold</span>
                    </>
                  ) : (
                    <>
                      <Pause size={16} className="mr-1 text-white" />
                      <span className="text-white">Hold</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Form Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Call Type */}
        <div>
          <label
            className="block text-sm font-medium"
            style={{ color: "#7e7e7e" }}
          >
            Call Type *
          </label>
          <Select
            value={callTypes.find((type) => type.value === formData.callType)}
            onChange={(selectedOption) =>
              setFormData((prev) => ({
                ...prev,
                callType: selectedOption?.value || "",
              }))
            }
            options={callTypes}
            styles={selectStyles}
            placeholder="Select call type"
            className="mt-1"
            isSearchable={false}
          />
          {errors.callType && (
            <p className="mt-1 text-sm text-red-600">{errors.callType}</p>
          )}
        </div>

        {/* Call Status */}
        <div>
          <label
            className="block text-sm font-medium"
            style={{ color: "#7e7e7e" }}
          >
            Call Status *
          </label>
          <Select
            value={callStatuses.find(
              (status) => status.value === formData.callStatus
            )}
            onChange={(selectedOption) =>
              setFormData((prev) => ({
                ...prev,
                callStatus: selectedOption?.value || "",
              }))
            }
            options={callStatuses}
            styles={selectStyles}
            placeholder="Select call status"
            className="mt-1"
            isSearchable={false}
          />
          {errors.callStatus && (
            <p className="mt-1 text-sm text-red-600">{errors.callStatus}</p>
          )}
        </div>
      </div>

      {/* Purpose */}
      <div>
        <label
          className="block text-sm font-medium"
          style={{ color: "#7e7e7e" }}
        >
          Purpose *
        </label>
        <Select
          value={purposes.find(
            (purpose) => purpose.value === formData.purposeId
          )}
          onChange={(selectedOption) =>
            setFormData((prev) => ({
              ...prev,
              purposeId: selectedOption?.value || "",
            }))
          }
          options={purposes}
          styles={selectStyles}
          placeholder={
            purposesLoading ? "Loading purposes..." : "Select purpose"
          }
          className="mt-1"
          isLoading={purposesLoading}
          isDisabled={purposesLoading}
        />
        {errors.purposeId && (
          <p className="mt-1 text-sm text-red-600">{errors.purposeId}</p>
        )}
      </div>

      {/* Notes */}
      <div>
        <label
          className="block text-sm font-medium"
          style={{ color: "#7e7e7e" }}
        >
          Notes
        </label>
        <textarea
          name="notes"
          value={formData.notes}
          onChange={handleInputChange}
          rows={4}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="Add any notes about the call..."
        />
      </div>

      {/* Follow-up Date and Time */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label
            className="block text-sm font-medium"
            style={{ color: "#7e7e7e" }}
          >
            Follow-up Date
          </label>
          <input
            type="date"
            name="followUpDate"
            value={formData.followUpDate}
            onChange={handleInputChange}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            className="block text-sm font-medium"
            style={{ color: "#7e7e7e" }}
          >
            Follow-up Time
          </label>
          <input
            type="time"
            name="followUpTime"
            value={formData.followUpTime}
            onChange={handleInputChange}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Attachments */}
      <div>
        <label
          className="block text-sm font-medium"
          style={{ color: "#7e7e7e" }}
        >
          Attachments
        </label>
        <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
          <div className="space-y-1 text-center">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <div className="flex text-sm text-gray-600">
              <label
                htmlFor="file-upload"
                className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
              >
                <span>Upload files</span>
                <input
                  id="file-upload"
                  name="file-upload"
                  type="file"
                  className="sr-only"
                  multiple
                  onChange={handleFileUpload}
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
                />
              </label>
              <p className="pl-1">or drag and drop</p>
            </div>
            <p className="text-xs text-gray-500">
              PDF, DOC, DOCX, JPG, PNG, GIF up to 10MB each
            </p>
          </div>
        </div>

        {/* Uploaded Files List */}
        {formData.attachments.length > 0 && (
          <div className="mt-3 space-y-2">
            {formData.attachments.map((attachment) => (
              <div
                key={attachment.id}
                className="flex items-center justify-between p-2 bg-gray-50 rounded-lg border"
              >
                <div className="flex items-center gap-2">
                  <div className="text-sm" style={{ color: "#7e7e7e" }}>
                    <div className="font-medium">{attachment.name}</div>
                    <div className="text-xs" style={{ color: "#9ca3af" }}>
                      {(attachment.size / 1024).toFixed(1)} KB •{" "}
                      {attachment.type || "Unknown type"}
                    </div>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => removeAttachment(attachment.id)}
                  className="p-1 text-red-500 hover:text-red-700 transition-colors"
                >
                  <X size={16} />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-4 py-2 bg-[#165026] hover:bg-green-700 text-white rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? "Saving..." : "Save Call"}
        </button>
      </div>
    </form>
  );
};

export default CallToDoForm;
