import { useState } from "react";
import Select from "react-select";

const CustomerServiceCallForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    callStatus: "",
    customerFeedbackCategory: "",
    comments: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Call status options
  const callStatuses = [
    { value: "completed", label: "Completed" },
    { value: "no_answer", label: "No Answer" },
  ];

  // Fake customer feedback categories (will be replaced with API data later)
  const customerFeedbackCategories = [
    { value: "complaint", label: "Complaint" },
    { value: "inquiry", label: "Inquiry" },
    { value: "compliment", label: "Compliment" },
    { value: "suggestion", label: "Suggestion" },
    { value: "technical_support", label: "Technical Support" },
    { value: "billing_issue", label: "Billing Issue" },
    { value: "service_request", label: "Service Request" },
    { value: "follow_up", label: "Follow Up" },
  ];

  // React Select styles to match the design from LeadForm
  const selectStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: "48px",
      border: state.isFocused
        ? "2px solid #10b981"
        : errors.customerFeedbackCategory || errors.callStatus
        ? "1px solid #ef4444"
        : "1px solid #d1d5db",
      borderRadius: "8px",
      boxShadow: "none",
      "&:hover": {
        borderColor: state.isFocused ? "#10b981" : "#9ca3af",
      },
      backgroundColor: "white",
      fontSize: "14px",
    }),
    placeholder: (provided) => ({
      ...provided,
      color: "#9ca3af",
    }),
    singleValue: (provided) => ({
      ...provided,
      color: "#111827",
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 9999,
      border: "1px solid #e5e7eb",
      borderRadius: "8px",
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "#10b981"
        : state.isFocused
        ? "#f0fdf4"
        : "white",
      color: state.isSelected ? "white" : "#374151",
      "&:hover": {
        backgroundColor: state.isSelected ? "#10b981" : "#f0fdf4",
      },
    }),
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.callStatus) {
      newErrors.callStatus = "Call status is required";
    }

    if (!formData.customerFeedbackCategory) {
      newErrors.customerFeedbackCategory = "Customer feedback category is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handleSelectChange = (selectedOption, actionMeta) => {
    const { name } = actionMeta;
    const value = selectedOption ? selectedOption.value : "";
    
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user selects
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      console.log("Customer service call submitted:", { formData, item });
      
      // Call the onSubmit callback with the form data
      onSubmit?.(formData, item);
      onClose();
    } catch (error) {
      console.error("Error submitting customer service call:", error);
      onSubmit?.(null, item, error);
      onClose();
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-6">
      {/* Call Status */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Call Status *
        </label>
        <Select
          name="callStatus"
          value={callStatuses.find(
            (status) => status.value === formData.callStatus
          )}
          onChange={handleSelectChange}
          options={callStatuses}
          styles={selectStyles}
          placeholder="Select call status"
          isSearchable={false}
          className="react-select-container"
          classNamePrefix="react-select"
        />
        {errors.callStatus && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {errors.callStatus}
          </p>
        )}
      </div>

      {/* Customer Feedback Category */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Customer Feedback Category *
        </label>
        <Select
          name="customerFeedbackCategory"
          value={customerFeedbackCategories.find(
            (category) => category.value === formData.customerFeedbackCategory
          )}
          onChange={handleSelectChange}
          options={customerFeedbackCategories}
          styles={selectStyles}
          placeholder="Select feedback category"
          isSearchable
          className="react-select-container"
          classNamePrefix="react-select"
        />
        {errors.customerFeedbackCategory && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {errors.customerFeedbackCategory}
          </p>
        )}
      </div>

      {/* Comments */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Comments
        </label>
        <textarea
          name="comments"
          value={formData.comments}
          onChange={handleInputChange}
          rows={4}
          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400 resize-vertical"
          placeholder="Add any comments about the call..."
        />
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
        <button
          type="button"
          onClick={onClose}
          disabled={isSubmitting}
          className="px-6 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-6 py-3 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Processing...
            </>
          ) : (
            "Submit Call"
          )}
        </button>
      </div>
    </form>
  );
};

export default CustomerServiceCallForm;
