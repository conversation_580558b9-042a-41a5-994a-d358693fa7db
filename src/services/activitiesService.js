import instance from '../axios/instance.jsx';

// Activities API endpoints
const ENDPOINTS = {
  ACTIVITIES_BY_TYPE: '/activities-by-interaction-type',
};

// Activities Service
export const activitiesService = {
  // Get activities by interaction type (calls or visits)
  getByInteractionType: async (type) => {
    try {
      console.log(`=== FETCHING ${type.toUpperCase()} ACTIVITIES ===`);
      console.log(`API Endpoint: GET ${ENDPOINTS.ACTIVITIES_BY_TYPE}?type=${type}`);
      
      const response = await instance.get(ENDPOINTS.ACTIVITIES_BY_TYPE, {
        params: { type }
      });
      
      console.log(`${type} activities response:`, response.data);
      console.log(`Total ${type} activities:`, response.data.total);
      console.log('=======================================');
      
      return response.data;
    } catch (error) {
      console.error(`Error fetching ${type} activities:`, error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error(`${type} activities endpoint not found.`);
      } else if (error.response?.status === 403) {
        throw new Error(`You do not have permission to view ${type} activities.`);
      } else if (error.response?.status >= 500) {
        throw new Error(`Server error while fetching ${type} activities. Please try again later.`);
      } else {
        throw new Error(error.response?.data?.message || `Failed to fetch ${type} activities.`);
      }
    }
  },

  // Get calls specifically
  getCalls: async () => {
    return await activitiesService.getByInteractionType('call');
  },

  // Get visits specifically
  getVisits: async () => {
    return await activitiesService.getByInteractionType('visit');
  },
};

/**
 * Format activities data for table display
 * @param {Object} apiResponse - Response from activities API
 * @param {string} type - Type of activity ('call' or 'visit')
 * @returns {Array} - Formatted data for DataTable
 */
export const formatActivitiesForTable = (apiResponse, type) => {
  if (!apiResponse || !apiResponse.data) {
    return [];
  }

  return apiResponse.data.map((activity, index) => ({
    id: activity.id || `${type}${index + 1}`,
    name: activity.lead_name || "Unknown Lead",
    anchor: activity.performed_by?.name || "Unknown",
    mobile: activity.lead_phone || "No phone",
    madeBy: activity.performed_by?.name || "Unknown",
    status: type === 'call' ? activity.call_status : activity.visit_status,
    date: formatActivityDate(activity.created_at),
    // Additional fields for detailed view
    clientId: activity.lead_client_id,
    activityType: activity.activity_type,
    notes: activity.notes,
    duration: activity.call_duration_minutes, // for calls only
    nextFollowup: activity.next_followup_date,
    performedBy: activity.performed_by,
    purpose: activity.purpose,
    createdAt: activity.created_at,
    interactionType: activity.interaction_type,
  }));
};

/**
 * Format activity date for display
 * @param {string} dateString - ISO date string
 * @returns {string} - Formatted date
 */
const formatActivityDate = (dateString) => {
  if (!dateString) return "Unknown date";
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  } catch (error) {
    console.warn('Error formatting date:', dateString);
    return "Invalid date";
  }
};

/**
 * Get status color for activities
 * @param {string} status - Activity status
 * @param {string} type - Activity type ('call' or 'visit')
 * @returns {Object} - Color classes
 */
export const getActivityStatusColor = (status, type) => {
  const statusLower = status?.toLowerCase() || '';
  
  // Common status colors
  const statusColors = {
    success: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
    successful: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
    completed: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
    failed: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
    cancelled: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",
    pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
    scheduled: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
    'in-progress': "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400",
  };

  return statusColors[statusLower] || "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
};

/**
 * Get activity type icon
 * @param {string} type - Activity type ('call' or 'visit')
 * @returns {string} - Icon name or emoji
 */
export const getActivityTypeIcon = (type) => {
  const icons = {
    call: "📞",
    visit: "🏠",
  };
  
  return icons[type] || "📋";
};

/**
 * Format duration for display
 * @param {number} minutes - Duration in minutes
 * @returns {string} - Formatted duration
 */
export const formatDuration = (minutes) => {
  if (!minutes || minutes === 0) return "N/A";
  
  if (minutes < 60) {
    return `${minutes} min`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h ${remainingMinutes}m`;
};

export default activitiesService;
