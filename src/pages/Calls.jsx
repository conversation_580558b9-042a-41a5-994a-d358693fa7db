import { useState, useEffect } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import CallForm from "../components/forms/CallForm";
import VisitForm from "../components/forms/VisitForm";
import { activitiesService, formatActivitiesForTable, getActivityStatusColor } from "../services/activitiesService";
import { callsService } from "../services/callsService";
import { toast } from 'react-toastify';

const Calls = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [calls, setCalls] = useState([]);
  const [error, setError] = useState(null);

  // Define columns of the table here and what to render
  const columns = [
    
    {
      key: "name",
      title: "NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "anchor",
      title: "ANCHOR",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "mobile",
      title: "MOBILE",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          {value}
        </span>
      ),
    },

     {
      key: "madeBy",
      title: "MADE BY",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          {value}
        </span>
      ),
    },
    {
      key: "status",
      title: "STATUS",
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getActivityStatusColor(value, 'call')}`}>
          {value || 'Unknown'}
        </span>
      ),
    },
   
    {
      key: "date",
      title: "DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
   
  ];

  // Fetch calls data from API
  const fetchCalls = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching calls data...");

      const response = await activitiesService.getCalls();
      const formattedCalls = formatActivitiesForTable(response, 'call');

      setCalls(formattedCalls);
      console.log("Calls data loaded successfully:", formattedCalls.length, "calls");
    } catch (error) {
      console.error("Error fetching calls:", error);
      setError(error.message);
      toast.error(error.message || "Failed to load calls data");
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchCalls();
  }, []);

  // Form submission handlers
  const handleCreateSubmit = async (formData) => {
    try {
      console.log("Creating CALL:", formData);

      const newCall = await callsService.create(formData);

      // Add the new call to the state
      setCalls(prevCalls => [newCall, ...prevCalls]);

      toast.success("Call created successfully!");
      console.log("Call added to table state");
    } catch (error) {
      console.error("Error creating call:", error);
      toast.error(error.message || "Failed to create call");
    }
  };

  const handleEditSubmit = async (updatedCallData, originalItem) => {
    try {
      console.log("Updating call in table:", {
        originalItem,
        updatedData: updatedCallData,
      });

      const updatedCall = await callsService.update(originalItem.id, updatedCallData);

      // Update the call in state
      setCalls(prevCalls =>
        prevCalls.map(call =>
          call.id === originalItem.id ? { ...call, ...updatedCall } : call
        )
      );

      toast.success("Call updated successfully!");
      console.log("Call updated in table state");
    } catch (error) {
      console.error("Error updating call in table:", error);
      toast.error(error.message || "Failed to update call");
    }
  };

  const handleDeleteConfirm = async (call) => {
    try {
      console.log("Deleting call:", call);
      console.log(`Making DELETE request to /calls/${call.id}`);

      const success = await callsService.delete(call.id);

      if (success) {
        // Remove the call from state
        setCalls(prevCalls => prevCalls.filter(c => c.id !== call.id));
        toast.success("Call deleted successfully!");
        console.log("Call removed from table state");
      } else {
        throw new Error("Delete operation did not return success status");
      }
    } catch (error) {
      console.error("Error deleting call:", error);
      toast.error(error.message || "Failed to delete call");
    }
  };

  const handleView = (call) => {
    console.log("View call:", call);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = async () => {
    setLoadingMore(true);
    try {
      console.log("Loading more calls...");
      // For now, just refresh the data
      // In a real implementation, you might have pagination
      await fetchCalls();
    } catch (error) {
      console.error("Error loading more calls:", error);
      toast.error("Failed to load more calls");
    } finally {
      setLoadingMore(false);
    }
  };

  const handleCallSubmit = async (callData, call, error) => {
    console.log("Call submitted:", { callData, call, error });

    // If call was created successfully (no error), update the state
    if (!error && callData && call) {
      console.log("Call created successfully, updating call state...");

      // Update the call in the table to reflect new call count
      setCalls(prevCalls =>
        prevCalls.map(c =>
          c.id === call.id
            ? {
                ...c,
                calls: (parseInt(c.calls) || 0) + 1,
                lastInteraction: new Date().toISOString().split('T')[0],
                lastInteractionType: "call"
              }
            : c
        )
      );

      console.log("Call state updated after call creation");
    }
  };

  const handleVisitSubmit = async (visitData, call, error) => {
    console.log("Visit submitted:", { visitData, call, error });

    // If visit was created successfully (no error), update the state
    if (!error && visitData && call) {
      console.log("Visit created successfully, updating call state...");

      // Update the call in the table to reflect new visit count
      setCalls(prevCalls =>
        prevCalls.map(c =>
          c.id === call.id
            ? {
                ...c,
                visits: (parseInt(c.visits) || 0) + 1,
                lastInteraction: new Date().toISOString().split('T')[0],
                lastInteractionType: "visit"
              }
            : c
        )
      );

      console.log("Call state updated after visit creation");
    }
  };

  return (
    <PrivateLayout>
      {/* Data Table */}
      <div className="">
        <DataTable
          columns={columns}
          data={calls}
          searchPlaceholder="Search calls..."
          addButtonText="New Call"
          onView={handleView}
          actions={["call", "visit", "edit", "delete"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Calls"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="addedBy"
          highlightColors={{
            BUSINESS:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            PERSONAL:
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
          }}
          // Modal forms
          createForm={({ onClose }) => (
            <CallForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <CallForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Call"
            />
          )}
          callForm={({ item, onClose }) => (
            <CallForm
              item={item}
              onClose={onClose}
              onSubmit={handleCallSubmit}
            />
          )}
          visitForm={({ item, onClose }) => (
            <VisitForm
              item={item}
              onClose={onClose}
              onSubmit={handleVisitSubmit}
            />
          )}
          createModalTitle="Create New Call"
          editModalTitle="Edit Call"
          deleteModalTitle=""
          callModalTitle="Make Call"
          visitModalTitle="Schedule Visit"
          modalSize="lg"
          deleteModalSize="sm"
          callModalSize="lg"
          visitModalSize="lg"
        />

      </div>

    </PrivateLayout>
  
  )
}

export default Calls
