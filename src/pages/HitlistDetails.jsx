import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { ArrowLeft, Phone, History, Edit, Trash2 } from "lucide-react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";

const HitlistDetails = () => {
  const { hitlistCode } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  // Demo data for hitlist details
  const hitlistDetails = {
    code: hitlistCode || "HIT-JULY-001",
    uploaded_date: "2025-08-01T09:22:00Z",
    uploaded_by: "<PERSON>",
    type: hitlistCode?.includes("002") ? "2by2by2" : "Dormancy",
    total_customers: 40,
    calls_made: 25,
    call_progress: 62.5,
  };

  // Demo data for customers based on type
  const getDemoCustomers = () => {
    if (hitlistDetails.type === "Dormancy") {
      return [
        {
          id: 1,
          customer_name: "<PERSON>e",
          account_number: "***********",
          phone_number: "**********",
          assigned_agent: "Alice Mwangi",
          rm_code: "RM123",
          call_status: "Completed",
          call_date: "2025-08-02T10:15:00Z",
        },
        {
          id: 2,
          customer_name: "Jane Smith",
          account_number: "***********",
          phone_number: "**********",
          assigned_agent: "Bob Kiprotich",
          rm_code: "RM456",
          call_status: "Pending",
          call_date: null,
        },
        {
          id: 3,
          customer_name: "Peter Mwangi",
          account_number: "***********",
          phone_number: "**********",
          assigned_agent: "Carol Wanjiku",
          rm_code: "RM789",
          call_status: "Not Started",
          call_date: null,
        },
        {
          id: 4,
          customer_name: "Mary Wanjiru",
          account_number: "***********",
          phone_number: "**********",
          assigned_agent: "David Kimani",
          rm_code: "RM101",
          call_status: "Completed",
          call_date: "2025-07-30T16:20:00Z",
        },
        {
          id: 5,
          customer_name: "Samuel Ochieng",
          account_number: "***********",
          phone_number: "**********",
          assigned_agent: "Grace Njeri",
          rm_code: "RM202",
          call_status: "Pending",
          call_date: null,
        },
      ];
    } else {
      // 2by2by2 type
      return [
        {
          id: 1,
          customer_name: "John Doe",
          account_number: "***********",
          assigned_agent: "Alice Mwangi",
          first_2_status: {
            status: "Completed",
            call_date: "2025-08-02T10:15:00Z",
            notes: "Customer was interested",
          },
          second_2_status: {
            status: "Pending",
            call_date: null,
            notes: null,
          },
          third_2_status: {
            status: "Not Started",
            call_date: null,
            notes: null,
          },
        },
        {
          id: 2,
          customer_name: "Jane Smith",
          account_number: "***********",
          assigned_agent: "Bob Kiprotich",
          first_2_status: {
            status: "Completed",
            call_date: "2025-08-01T14:30:00Z",
            notes: "Follow up needed",
          },
          second_2_status: {
            status: "Completed",
            call_date: "2025-08-03T09:45:00Z",
            notes: "Customer agreed to meeting",
          },
          third_2_status: {
            status: "Pending",
            call_date: null,
            notes: null,
          },
        },
        {
          id: 3,
          customer_name: "Peter Mwangi",
          account_number: "***********",
          assigned_agent: "Carol Wanjiku",
          first_2_status: {
            status: "Not Started",
            call_date: null,
            notes: null,
          },
          second_2_status: {
            status: "Not Started",
            call_date: null,
            notes: null,
          },
          third_2_status: {
            status: "Not Started",
            call_date: null,
            notes: null,
          },
        },
      ];
    }
  };

  const customers = getDemoCustomers();

  // Format date function
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Status badge component
  const StatusBadge = ({ status }) => {
    const getStatusColor = (status) => {
      switch (status?.toLowerCase()) {
        case "completed":
          return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
        case "pending":
          return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
        case "not started":
          return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
        default:
          return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      }
    };

    return (
      <span
        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
          status
        )}`}
      >
        {status || "-"}
      </span>
    );
  };

  // Define columns based on hitlist type
  const getColumns = () => {
    if (hitlistDetails.type === "Dormancy") {
      return [
        {
          key: "customer_name",
          title: "CUSTOMER",
          render: (value) => (
            <span className="font-medium text-gray-900 dark:text-white">
              {value}
            </span>
          ),
        },
        {
          key: "account_number",
          title: "ACCOUNT NUMBER",
          render: (value) => (
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {value || "-"}
            </span>
          ),
        },
        {
          key: "phone_number",
          title: "PHONE NUMBER",
          render: (value) => (
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {value || "-"}
            </span>
          ),
        },
        {
          key: "assigned_agent",
          title: "ASSIGNED AGENT",
          render: (value) => (
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {value || "-"}
            </span>
          ),
        },
        {
          key: "rm_code",
          title: "AGENT RM CODE",
          render: (value) => (
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {value || "-"}
            </span>
          ),
        },
        {
          key: "call_status",
          title: "CALL STATUS",
          render: (value) => <StatusBadge status={value} />,
        },
        {
          key: "call_date",
          title: "CALL DATE",
          render: (value) => (
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {formatDate(value)}
            </span>
          ),
        },
      ];
    } else {
      // 2by2by2 columns
      return [
        {
          key: "customer_name",
          title: "CUSTOMER",
          render: (value) => (
            <span className="font-medium text-gray-900 dark:text-white">
              {value}
            </span>
          ),
        },
        {
          key: "account_number",
          title: "ACCOUNT NUMBER",
          render: (value) => (
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {value || "-"}
            </span>
          ),
        },
        {
          key: "assigned_agent",
          title: "ASSIGNED AGENT",
          render: (value) => (
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {value || "-"}
            </span>
          ),
        },
        {
          key: "first_2_status",
          title: "1ST 2 STATUS",
          render: (value) => <StatusBadge status={value?.status} />,
        },
        {
          key: "second_2_status",
          title: "2ND 2 STATUS",
          render: (value) => <StatusBadge status={value?.status} />,
        },
        {
          key: "third_2_status",
          title: "3RD 2 STATUS",
          render: (value) => <StatusBadge status={value?.status} />,
        },
      ];
    }
  };

  // Event handlers
  const handleView = (item) => {
    setSelectedItem(item);
    console.log("View item:", item);
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more customers");
    }, 2000);
  };

  const handleCustomAction = (action, item) => {
    console.log("Custom action:", action, "for item:", item);
    setSelectedItem(item);

    switch (action) {
      case "call-now":
        console.log("Initiating call for:", item.customer_name);
        break;
      case "call-history":
        console.log("Viewing call history for:", item.customer_name);
        break;
      default:
        console.log("Unhandled custom action:", action);
    }
  };

  const handleEdit = (item) => {
    console.log("Edit customer:", item);
  };

  const handleDelete = (item) => {
    console.log("Delete customer:", item);
  };

  const handleBackToHitlists = () => {
    navigate("/customer-service/hitlist");
  };

  // Export/Print handlers
  const handleExport = () => {
    console.log("Exporting hitlist details");
    // API integration logic here
  };

  const handlePrint = () => {
    console.log("Printing hitlist details");
    // Print functionality here
  };

  return (
    <PrivateLayout pageTitle={`Hitlist Details - ${hitlistDetails.code}`}>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackToHitlists}
                className="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-200"
              >
                <ArrowLeft size={16} className="mr-2" />
                Back to Hitlists
              </button>
              <h1 className="text-2xl font-semibold text-gray-900">
                {hitlistDetails.code}
              </h1>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <p className="text-sm text-gray-500">Upload Date & Time</p>
              <p className="text-lg font-medium text-gray-900">
                {formatDateTime(hitlistDetails.uploaded_date)}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Uploaded By</p>
              <p className="text-lg font-medium text-gray-900">
                {hitlistDetails.uploaded_by}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Type</p>
              <p className="text-lg font-medium text-gray-900">
                {hitlistDetails.type}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Customers</p>
              <p className="text-lg font-medium text-gray-900">
                {hitlistDetails.total_customers}
              </p>
            </div>
          </div>

          {/* Call Progress */}
          <div className="mt-6">
            <p className="text-sm text-gray-500 mb-2">Call Progress</p>
            <div className="flex items-center space-x-4">
              <div className="flex-1 bg-gray-200 rounded-full h-3 dark:bg-gray-700">
                <div
                  className="bg-green-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${hitlistDetails.call_progress}%` }}
                ></div>
              </div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {hitlistDetails.calls_made}/{hitlistDetails.total_customers}{" "}
                calls made ({hitlistDetails.call_progress}%)
              </span>
            </div>
          </div>
        </div>

        {/* Data Table */}
        <DataTable
          columns={getColumns()}
          data={customers}
          searchPlaceholder="Search customers..."
          onView={handleView}
          actions={["edit", "delete", "call-now", "call-history"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Customers"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          dataCountLabel="customers"
          showDataCount={true}
          onCustomAction={handleCustomAction}
          customActionLabels={{
            "call-now": "Call Now",
            "call-history": "See Call History",
          }}
          // Export functionality - only export and print
          showImportExport={true}
          showImport={false}
          onExport={handleExport}
          onPrint={handlePrint}
        />
      </div>
    </PrivateLayout>
  );
};

export default HitlistDetails;
