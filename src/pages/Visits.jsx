import { useState, useEffect } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import VisitForm from "../components/forms/VisitForm";
import CallForm from "../components/forms/CallForm";
import { activitiesService, formatActivitiesForTable, getActivityStatusColor } from "../services/activitiesService";
import { visitsService } from "../services/visitsService";
import { toast } from 'react-toastify';

const Visits = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [visits, setVisits] = useState([]);
  const [error, setError] = useState(null);

  // Define columns of the table here and what to render
  const columns = [
    
    {
      key: "name",
      title: "NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "anchor",
      title: "ANCHOR",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "mobile",
      title: "MOBILE",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          {value}
        </span>
      ),
    },

     {
      key: "madeBy",
      title: "MADE BY",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          {value}
        </span>
      ),
    },
    {
      key: "status",
      title: "STATUS",
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getActivityStatusColor(value, 'visit')}`}>
          {value || 'Unknown'}
        </span>
      ),
    },
   
    {
      key: "date",
      title: "DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
   
  ];

  // Fetch visits data from API
  const fetchVisits = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching visits data...");

      const response = await activitiesService.getVisits();
      const formattedVisits = formatActivitiesForTable(response, 'visit');

      setVisits(formattedVisits);
      console.log("Visits data loaded successfully:", formattedVisits.length, "visits");
    } catch (error) {
      console.error("Error fetching visits:", error);
      setError(error.message);
      toast.error(error.message || "Failed to load visits data");
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchVisits();
  }, []);

  // Form submission handlers
  const handleCreateSubmit = async (formData) => {
    try {
      console.log("Creating VISIT:", formData);

      const newVisit = await visitsService.create(formData);

      // Add the new visit to the state
      setVisits(prevVisits => [newVisit, ...prevVisits]);

      toast.success("Visit created successfully!");
      console.log("Visit added to table state");
    } catch (error) {
      console.error("Error creating visit:", error);
      toast.error(error.message || "Failed to create visit");
    }
  };

  const handleEditSubmit = async (updatedVisitData, originalItem) => {
    try {
      console.log("Updating visit in table:", {
        originalItem,
        updatedData: updatedVisitData,
      });

      const updatedVisit = await visitsService.update(originalItem.id, updatedVisitData);

      // Update the visit in state
      setVisits(prevVisits =>
        prevVisits.map(visit =>
          visit.id === originalItem.id ? { ...visit, ...updatedVisit } : visit
        )
      );

      toast.success("Visit updated successfully!");
      console.log("Visit updated in table state");
    } catch (error) {
      console.error("Error updating visit in table:", error);
      toast.error(error.message || "Failed to update visit");
    }
  };

  const handleDeleteConfirm = async (visit) => {
    try {
      console.log("Deleting visit:", visit);
      console.log(`Making DELETE request to /visits/${visit.id}`);

      const success = await visitsService.delete(visit.id);

      if (success) {
        // Remove the visit from state
        setVisits(prevVisits => prevVisits.filter(v => v.id !== visit.id));
        toast.success("Visit deleted successfully!");
        console.log("Visit removed from table state");
      } else {
        throw new Error("Delete operation did not return success status");
      }
    } catch (error) {
      console.error("Error deleting visit:", error);
      toast.error(error.message || "Failed to delete visit");
    }
  };

  const handleView = (visit) => {
    console.log("View visit:", visit);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = async () => {
    setLoadingMore(true);
    try {
      console.log("Loading more visits...");
      // For now, just refresh the data
      // In a real implementation, you might have pagination
      await fetchVisits();
    } catch (error) {
      console.error("Error loading more visits:", error);
      toast.error("Failed to load more visits");
    } finally {
      setLoadingMore(false);
    }
  };

  const handleCallSubmit = async (callData, visit, error) => {
    console.log("Call submitted:", { callData, visit, error });

    // If call was created successfully (no error), update the state
    if (!error && callData && visit) {
      console.log("Call created successfully, updating visit state...");

      // Update the visit in the table to reflect new call count
      setVisits(prevVisits =>
        prevVisits.map(v =>
          v.id === visit.id
            ? {
                ...v,
                calls: (parseInt(v.calls) || 0) + 1,
                lastInteraction: new Date().toISOString().split('T')[0],
                lastInteractionType: "call"
              }
            : v
        )
      );

      console.log("Visit state updated after call creation");
    }
  };

  const handleVisitSubmit = async (visitData, visit, error) => {
    console.log("Visit submitted:", { visitData, visit, error });

    // If visit was created successfully (no error), update the state
    if (!error && visitData && visit) {
      console.log("Visit created successfully, updating visit state...");

      // Update the visit in the table to reflect new visit count
      setVisits(prevVisits =>
        prevVisits.map(v =>
          v.id === visit.id
            ? {
                ...v,
                visits: (parseInt(v.visits) || 0) + 1,
                lastInteraction: new Date().toISOString().split('T')[0],
                lastInteractionType: "visit"
              }
            : v
        )
      );

      console.log("Visit state updated after visit creation");
    }
  };


  return (
    <PrivateLayout>
      {/* Data Table */}
      <div className="">
        <DataTable
          columns={columns}
          data={visits}
          searchPlaceholder="Search visits..."
          addButtonText="New Visit"
          onView={handleView}
          actions={["call", "visit", "edit", "delete"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Visits"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="addedBy"
          highlightColors={{
            BUSINESS:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            PERSONAL:
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
          }}
          // Modal forms
          createForm={({ onClose }) => (
            <VisitForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <VisitForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Visit"
            />
          )}
          callForm={({ item, onClose }) => (
            <CallForm
              item={item}
              onClose={onClose}
              onSubmit={handleCallSubmit}
            />
          )}
          visitForm={({ item, onClose }) => (
            <VisitForm
              item={item}
              onClose={onClose}
              onSubmit={handleVisitSubmit}
            />
          )}
          createModalTitle="Create New Visit"
          editModalTitle="Edit Visit"
          deleteModalTitle=""
          callModalTitle="Make Call"
          visitModalTitle="Schedule Visit"
          modalSize="lg"
          deleteModalSize="sm"
          callModalSize="lg"
          visitModalSize="lg"
        />

      </div>

    </PrivateLayout>
  
  )
}

export default Visits;
