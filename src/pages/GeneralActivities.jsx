import { useState, useEffect } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { Eye } from "lucide-react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";

const GeneralActivities = () => {
  const { type } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  // Filter state for DataTable filters
  const [filters, setFilters] = useState({
    type: "", // For hitlist type filter (2by2by2, Dormancy)
  });

  // Demo data for hitlist table
  const hitlistData = [
    {
      id: 1,
      code: "HIT-JULY-001",
      type: "Dormancy",
      uploaded_by: "<PERSON>",
      upload_date: "2025-08-01T09:22:00Z",
      customer_count: 45,
      completion: 80,
    },
    {
      id: 2,
      code: "HIT-JULY-002",
      type: "2by2by2",
      uploaded_by: "<PERSON>",
      upload_date: "2025-07-28T14:15:00Z",
      customer_count: 120,
      completion: 65,
    },
    {
      id: 3,
      code: "HIT-JULY-003",
      type: "Dormancy",
      uploaded_by: "Mary Johnson",
      upload_date: "2025-07-25T11:30:00Z",
      customer_count: 78,
      completion: 92,
    },
  ];

  // Filter configuration for DataTable
  const filterConfig = [
    {
      key: "type",
      label: "Type",
      field: "type",
      placeholder: "All Types",
      selectedValue: filters.type,
      options: [
        { value: "2by2by2", label: "2by2by2" },
        { value: "Dormancy", label: "Dormancy" },
      ],
    },
  ];

  // Filter handlers
  const handleFilterChange = (filterKey, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      type: "",
    });
  };

  // Format date function
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  // Define columns for hitlist table
  const columns = [
    {
      key: "code",
      title: "HITLIST CODE",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "type",
      title: "TYPE",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "uploaded_by",
      title: "UPLOADED BY",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "upload_date",
      title: "UPLOAD DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {formatDate(value)}
        </span>
      ),
    },
    {
      key: "customer_count",
      title: "# OF CUSTOMERS",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "completion",
      title: "COMPLETION",
      render: (value) => (
        <div className="flex items-center space-x-2">
          <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700 min-w-[80px]">
            <div
              className="bg-green-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${value}%` }}
            ></div>
          </div>
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300 min-w-[40px]">
            {value}%
          </span>
        </div>
      ),
    },
    {
      key: "actions",
      title: "ACTIONS",
      render: (value, row) => (
        <button
          onClick={() => handleView(row)}
          className="p-1 hover:bg-gray-100 rounded transition-colors"
          title="View Details"
        >
          <Eye size={16} className="text-gray-500 hover:text-gray-700" />
        </button>
      ),
    },
  ];

  // Event handlers
  const handleView = (item) => {
    setSelectedItem(item);
    console.log("View item:", item);
    // Navigate to hitlist details page
    navigate(`/customer-service/hitlist/${item.code}`);
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more hitlist entries");
    }, 2000);
  };

  // Import/Export handlers
  const handleImport = (importData) => {
    console.log("Importing hitlist data:", importData);
    // API integration logic here
  };

  const handleExport = () => {
    console.log("Exporting hitlist data");
    // API integration logic here
  };

  const handlePrint = () => {
    console.log("Printing hitlist data");
    // Print functionality here
  };

  const handleDownloadTemplate = () => {
    console.log("Downloading hitlist template");
    // Template download logic here
  };

  return (
    <PrivateLayout pageTitle="Customer Service - Hitlist">
      <div className="space-y-6">
        {/* Data Table with Filters */}
        <DataTable
          columns={columns}
          data={hitlistData}
          searchPlaceholder="Search hitlists..."
          onView={handleView}
          actions={[]} // Hide default actions column
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Hitlists"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          dataCountLabel="hitlists"
          showDataCount={true}
          // Filters
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          // Import/Export functionality
          showImportExport={true}
          onImport={handleImport}
          onExport={handleExport}
          onPrint={handlePrint}
          importModalTitle="Import Hitlists"
          importTemplateFileName="Hitlists-Template.xlsx"
          importAcceptedFileTypes=".xlsx,.xls,.csv"
          // Custom import props for hitlists
          showHitlistTypeSelection={true}
          isHitlistImport={true}
          onDownloadTemplate={handleDownloadTemplate}
        />
      </div>
    </PrivateLayout>
  );
};

export default GeneralActivities;
