import { useState, useEffect } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import { formatDateRange } from "../utils/dateUtils";

const MyTargets = () => {
  const [loading, setLoading] = useState(true);
  const [targets, setTargets] = useState([]);

  // Filter states
  const [filters, setFilters] = useState({
    metric: "",
    frequency: "",
  });

  // Simulate loading data
  useEffect(() => {
    const loadTargets = () => {
      setLoading(true);
      // Simulate API call delay
      setTimeout(() => {
        setTargets(generateFakeData());
        setLoading(false);
      }, 1000);
    };

    loadTargets();
  }, []);

  // Generate fake data for demonstration
  const generateFakeData = () => {
    const today = new Date();
    const getRandomDate = (daysFromNow) => {
      const date = new Date(today);
      date.setDate(date.getDate() + daysFromNow);
      return date.toISOString().split('T')[0];
    };

    const getRandomProgress = () => Math.floor(Math.random() * 100);
    const getRandomCompleted = (target) => Math.floor(Math.random() * target);

    return [
      {
        id: 1,
        metric: "Call",
        frequency: "daily",
        value: 20,
        start_date: getRandomDate(-30),
        end_date: getRandomDate(30),
        completed: getRandomCompleted(20),
        progress: getRandomProgress(),
      },
      {
        id: 2,
        metric: "Visit",
        frequency: "weekly",
        value: 5,
        start_date: getRandomDate(-14),
        end_date: getRandomDate(14),
        completed: getRandomCompleted(5),
        progress: getRandomProgress(),
      },
      {
        id: 3,
        metric: "Call",
        frequency: "custom",
        value: 100,
        start_date: getRandomDate(-60),
        end_date: getRandomDate(60),
        completed: getRandomCompleted(100),
        progress: getRandomProgress(),
      },
      {
        id: 4,
        metric: "Visit",
        frequency: "daily",
        value: 3,
        start_date: getRandomDate(-7),
        end_date: getRandomDate(23),
        completed: getRandomCompleted(3),
        progress: getRandomProgress(),
      },
      {
        id: 5,
        metric: "Call",
        frequency: "weekly",
        value: 50,
        start_date: getRandomDate(-21),
        end_date: getRandomDate(21),
        completed: getRandomCompleted(50),
        progress: getRandomProgress(),
      },
    ];
  };

  // Calculate days remaining
  const calculateDaysRemaining = (endDate) => {
    const today = new Date();
    const end = new Date(endDate);
    const diffTime = end - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Filter data based on filters
  const filteredTargets = targets.filter((target) => {
    const matchesMetric = filters.metric === "" || target.metric === filters.metric;
    const matchesFrequency = filters.frequency === "" || target.frequency === filters.frequency;
    return matchesMetric && matchesFrequency;
  });

  // Filter configuration for DataTable
  const filterConfig = [
    {
      key: "metric",
      label: "Metric",
      field: "metric",
      placeholder: "All Metrics",
      selectedValue: filters.metric,
      options: [
        { value: "Call", label: "Call" },
        { value: "Visit", label: "Visit" },
      ],
    },
    {
      key: "frequency",
      label: "Frequency",
      field: "frequency",
      placeholder: "All Frequencies",
      selectedValue: filters.frequency,
      options: [
        { value: "daily", label: "Daily" },
        { value: "weekly", label: "Weekly" },
        { value: "custom", label: "Custom" },
      ],
    },
  ];

  // Filter handlers
  const handleFilterChange = (filterKey, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      metric: "",
      frequency: "",
    });
  };

  // Define columns for the table
  const columns = [
    {
      key: "metric",
      title: "Metric",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "frequency",
      title: "Frequency",
      render: (value) => (
        <span className="capitalize text-gray-700 dark:text-gray-300">
          {value}
        </span>
      ),
    },
    {
      key: "value",
      title: "Value",
      render: (value) => (
        <span className="font-semibold text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "start_end",
      title: "Start–End",
      render: (_, row) => (
        <span className="text-sm text-gray-700 dark:text-gray-300">
          {formatDateRange(row.start_date, row.end_date)}
        </span>
      ),
    },
    {
      key: "progress",
      title: "Progress",
      render: (value, row) => {
        const progressPercentage = Math.round((row.completed / row.value) * 100);
        
        return (
          <div className="flex items-center space-x-2">
            <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700 min-w-[80px]">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300 min-w-[40px]">
              {progressPercentage}%
            </span>
            <span className="text-xs text-gray-500 ml-1">
              ({row.completed}/{row.value})
            </span>
          </div>
        );
      },
    },
    {
      key: "days_remaining",
      title: "Days Remaining",
      render: (_, row) => {
        const daysRemaining = calculateDaysRemaining(row.end_date);
        const isOverdue = daysRemaining < 0;
        const isUrgent = daysRemaining <= 7 && daysRemaining >= 0;
        
        return (
          <span 
            className={`text-sm font-medium ${
              isOverdue 
                ? "text-red-600 dark:text-red-400" 
                : isUrgent 
                ? "text-orange-600 dark:text-orange-400" 
                : "text-gray-700 dark:text-gray-300"
            }`}
          >
            {isOverdue ? `${Math.abs(daysRemaining)} days overdue` : `${daysRemaining} days`}
          </span>
        );
      },
    },
  ];

  return (
    <PrivateLayout pageTitle="My Targets">
      <div className="space-y-6">
        <DataTable
          columns={columns}
          data={filteredTargets}
          searchPlaceholder="Search my targets..."
          loading={loading}
          showDataCount={true}
          dataCountLabel="targets"
          // Filters
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          // No actions needed for personal targets view
          actions={[]}
        />
      </div>
    </PrivateLayout>
  );
};

export default MyTargets;
