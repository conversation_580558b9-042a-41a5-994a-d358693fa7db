import { useState, useEffect } from "react";
import { Phone } from "lucide-react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import Modal from "../components/common/Modal";
import CallToDoForm from "../components/forms/CallToDoForm";

const CallsToDo = () => {
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [showCallModal, setShowCallModal] = useState(false);

  // Demo data for calls to do
  const callsToDoData = [
    {
      id: 1,
      customer_name: "<PERSON>",
      account_number: "***********",
      phone_number: "**********",
      hitlist_type: "Dormancy",
      phase_step: "-",
      scheduled: "2025-08-04T09:00:00Z",
      status: "Pending",
    },
    {
      id: 2,
      customer_name: "<PERSON>",
      account_number: "***********",
      phone_number: "**********",
      hitlist_type: "2by2by2",
      phase_step: "First 2",
      scheduled: "2025-08-04T10:30:00Z",
      status: "Scheduled",
    },
    {
      id: 3,
      customer_name: "Peter Mwangi",
      account_number: "***********",
      phone_number: "**********",
      hitlist_type: "2by2by2",
      phase_step: "Second 2",
      scheduled: "2025-08-05T14:15:00Z",
      status: "Pending",
    },
    {
      id: 4,
      customer_name: "Mary Wanjiru",
      account_number: "***********",
      phone_number: "**********",
      hitlist_type: "Dormancy",
      phase_step: "-",
      scheduled: "2025-08-05T11:00:00Z",
      status: "Overdue",
    },
    {
      id: 5,
      customer_name: "Samuel Ochieng",
      account_number: "***********",
      phone_number: "**********",
      hitlist_type: "2by2by2",
      phase_step: "Third 2",
      scheduled: "2025-08-06T16:45:00Z",
      status: "Scheduled",
    },
  ];

  // Format date function
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  // Status badge component
  const StatusBadge = ({ status }) => {
    const getStatusColor = (status) => {
      switch (status?.toLowerCase()) {
        case "scheduled":
          return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
        case "pending":
          return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
        case "overdue":
          return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
        case "completed":
          return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
        default:
          return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      }
    };

    return (
      <span
        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
          status
        )}`}
      >
        {status || "-"}
      </span>
    );
  };

  // Customer hover tooltip component
  const CustomerCell = ({ customer_name, account_number, phone_number }) => {
    return (
      <div className="group relative">
        <div className="cursor-pointer">
          <div className="font-medium text-gray-900 dark:text-white">
            {customer_name}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {account_number}
          </div>
        </div>

        {/* Hover tooltip */}
        <div className="absolute left-0 top-full mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg p-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
          <div className="space-y-2">
            <div>
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Customer:
              </span>
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {customer_name}
              </div>
            </div>
            <div>
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Account Number:
              </span>
              <div className="text-sm text-gray-700 dark:text-gray-300">
                {account_number}
              </div>
            </div>
            <div>
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Phone Number:
              </span>
              <div className="text-sm text-gray-700 dark:text-gray-300">
                {phone_number}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Define columns for the table
  const columns = [
    {
      key: "customer",
      title: "CUSTOMER",
      render: (value, row) => (
        <CustomerCell
          customer_name={row.customer_name}
          account_number={row.account_number}
          phone_number={row.phone_number}
        />
      ),
    },
    {
      key: "phone_number",
      title: "PHONE NUMBER",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value || "-"}
        </span>
      ),
    },
    {
      key: "hitlist_type",
      title: "HITLIST TYPE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value || "-"}
        </span>
      ),
    },
    {
      key: "phase_step",
      title: "PHASE / STEP",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value || "-"}
        </span>
      ),
    },
    {
      key: "scheduled",
      title: "SCHEDULED",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {formatDate(value)}
        </span>
      ),
    },
    {
      key: "status",
      title: "STATUS",
      render: (value) => <StatusBadge status={value} />,
    },
    {
      key: "action",
      title: "ACTION",
      render: (value, row) => (
        <button
          onClick={() => handleCallNow(row)}
          className="inline-flex items-center px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
        >
          <Phone size={14} className="mr-1" />
          Call Now
        </button>
      ),
    },
  ];

  // Event handlers
  const handleView = (item) => {
    setSelectedItem(item);
    console.log("View item:", item);
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more calls to do");
    }, 2000);
  };

  const handleCallNow = (item) => {
    setSelectedItem(item);
    setShowCallModal(true);
    console.log("Initiating call for:", item.customer_name);
  };

  const handleCallSubmit = async (callData, customer, error) => {
    console.log("Call submitted:", { callData, customer, error });

    if (!error && callData && customer) {
      console.log("Call created successfully");
      // Update the calls to do state if needed
    }

    setShowCallModal(false);
    setSelectedItem(null);
  };

  // Import/Export handlers
  const handleExport = () => {
    console.log("Exporting calls to do data");
    // API integration logic here
  };

  const handlePrint = () => {
    console.log("Printing calls to do data");
    // Print functionality here
  };

  return (
    <PrivateLayout pageTitle="Calls To Do">
      <div className="space-y-6">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={callsToDoData}
          searchPlaceholder="Search calls to do..."
          onView={handleView}
          actions={[]} // Hide default actions column
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Calls"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          dataCountLabel="calls to do"
          showDataCount={true}
          highlightField="status"
          highlightColors={{
            Scheduled:
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
            Pending:
              "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
            Overdue:
              "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
            Completed:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
          }}
          // Export functionality - only export and print
          showImportExport={true}
          showImport={false}
          onExport={handleExport}
          onPrint={handlePrint}
        />

        {/* Call Modal */}
        {showCallModal && selectedItem && (
          <Modal
            isOpen={showCallModal}
            onClose={() => {
              setShowCallModal(false);
              setSelectedItem(null);
            }}
            title={`Make Call - ${selectedItem?.customer_name}`}
            size="lg"
          >
            <CallToDoForm
              item={selectedItem}
              onClose={() => {
                setShowCallModal(false);
                setSelectedItem(null);
              }}
              onSubmit={handleCallSubmit}
            />
          </Modal>
        )}
      </div>
    </PrivateLayout>
  );
};

export default CallsToDo;
